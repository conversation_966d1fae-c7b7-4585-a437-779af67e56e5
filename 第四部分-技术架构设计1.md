# 第四部分：技术架构设计

## 7. 核心架构设计

### 7.1 核心概念定义

#### 7.1.1 架构核心概念

在深入架构设计之前，我们需要先理解延康"云e养"平台中几个核心概念的含义和作用。

**Template（模板）**：
- **定义**：通过JSON配置文件定义的业务流程模板，包含业务执行步骤、参数、异常处理等
- **作用**：实现业务逻辑的配置化，无需修改代码即可调整业务流程
- **存储**：存储在PostgreSQL的JSONB字段中，支持版本管理和热更新
- **示例**：用户注册模板、订单处理模板、服务预约模板

**Component（组件）**：
- **定义**：封装特定业务功能的可复用单元，提供原子化的业务能力
- **特点**：高内聚、低耦合、可独立测试、可被多个Template复用
- **分类**：用户组件、订单组件、支付组件、通知组件等
- **实现**：使用@Component注解的Spring Bean

**Plugin（插件）**：
- **定义**：封装第三方系统集成能力的标准化接口
- **作用**：将外部服务能力标准化，隔离第三方依赖，便于替换和扩展
- **类型**：微信插件、支付插件、短信插件、地图插件等
- **设计原则**：统一接口、错误处理、重试机制、降级策略

**Tool（工具）**：
- **定义**：提供原子化技术能力的最小功能单元，不包含业务逻辑
- **特征**：无状态、纯技术功能、可被所有层复用
- **分类**：数据库工具、缓存工具、加密工具、验证工具等
- **职责**：为上层提供基础技术支撑

**Engine（引擎）**：
- **定义**：解析Template配置并协调Component执行的核心组件
- **功能**：模板解析、流程控制、事务管理、异常处理
- **工作流程**：加载Template → 解析步骤 → 调用Component → 组装结果
- **核心价值**：实现业务流程的动态编排

**Router（路由器）**：
- **定义**：根据请求特征选择合适Template的决策组件
- **路由依据**：URL路径、用户类型、时间条件、业务规则等
- **工作模式**：规则匹配 → Template选择 → Engine调度
- **扩展性**：支持动态添加路由规则

**Gateway（网关）**：
- **定义**：系统的统一入口，处理所有外部请求
- **核心功能**：身份认证、权限校验、限流熔断、负载均衡
- **技术选型**：Spring Cloud Gateway
- **附加能力**：请求日志、监控统计、协议转换、MCP协议适配

**AOP（切面）**：
- **定义**：面向切面编程，用于处理系统中的横切关注点，如日志、监控、缓存、事务等
- **作用**：将通用功能从业务逻辑中分离，提高代码复用性和可维护性
- **实现方式**：基于Spring AOP，通过注解或配置定义切入点和通知
- **应用位置**：作用于Engine层和Component层的方法调用

**MCP（Model Context Protocol）**：
- **定义**：模型上下文协议，由Anthropic提出的标准化协议，让AI模型能够安全地与外部系统交互
- **作用**：为AI模型提供工具调用能力，实现AI与业务系统的深度集成
- **集成方式**：通过Gateway层的MCP适配器，将MCP协议请求转换为内部API调用
- **应用场景**：AI健康管家调用平台服务、智能客服自动处理业务、数据查询与分析

**Dify**：
- **定义**：开源的LLM应用开发平台，提供可视化的AI工作流编排能力
- **作用**：快速构建AI应用，管理提示词，编排复杂的AI工作流
- **集成方式**：通过API与平台集成，使用MCP协议调用平台能力
- **核心价值**：降低AI应用开发门槛，实现业务流程的智能化

**AI Agent（AI智能体）**：
- **定义**：具有自主决策和执行能力的AI实体，能够理解任务、制定计划、执行动作
- **能力特征**：自然语言理解、任务规划、工具调用、结果验证
- **在平台中的角色**：AI健康管家、智能客服助手、数据分析助手
- **实现基础**：Spring AI框架 + 大语言模型 + MCP工具调用 + 向量数据库检索

#### 7.1.2 架构设计理念

延康"云e养"平台基于**配置驱动**和**组件化**的设计理念，构建了一个高度灵活、可扩展的技术架构：

**核心设计原则**：
1. **配置化优先**：业务逻辑通过配置定义，而非硬编码
2. **组件化设计**：功能模块化，提高复用性和可维护性
3. **分层解耦**：各层职责明确，降低耦合度
4. **插件化集成**：第三方服务通过插件接入，便于替换
5. **工具化支撑**：基础能力工具化，避免重复开发

#### 7.1.3 架构层次关系

```mermaid
graph TB
    subgraph "配置层"
        T[Template<br/>业务流程配置]
    end
    
    subgraph "路由层"
        G[Gateway<br/>统一入口] --> R[Router<br/>路由决策]
    end
    
    subgraph "执行层"
        E[Engine<br/>流程引擎]
    end
    
    subgraph "业务层"
        C[Component<br/>业务组件]
    end
    
    subgraph "集成层"
        P[Plugin<br/>第三方集成]
    end
    
    subgraph "基础层"
        TO[Tool<br/>基础工具]
    end
    
    R --> T
    R --> E
    T --> E
    E --> C
    C --> P
    C --> TO
    P --> TO
```

#### 7.1.4 请求处理流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as Gateway
    participant Router as Router
    participant DB as 数据库
    participant Engine as Engine
    participant Component as Component
    
    Client->>Gateway: 发送请求
    Gateway->>Router: 转发请求（已认证）
    Router->>Router: 分析请求特征
    Router->>DB: 查询匹配的Template
    DB-->>Router: 返回Template配置
    Router->>Engine: 传递Template和请求
    Engine->>Engine: 解析Template步骤
    loop 执行每个步骤
        Engine->>Component: 调用组件方法
        Component-->>Engine: 返回结果
    end
    Engine-->>Router: 返回执行结果
    Router-->>Gateway: 返回响应
    Gateway-->>Client: 返回最终结果
```

#### 7.1.5 命名规范

**架构层命名**：
- Gateway层：统一技术入口，使用"Gateway"后缀
- Router层：业务路由决策，使用"Router"后缀
- Engine层：执行引擎，统一使用"TemplateEngine"
- Component层：业务组件，使用"Component"后缀
- Plugin层：第三方集成，使用"Plugin"后缀
- Tool层：基础工具，使用"Tool"后缀

**Template命名**：
- 格式：[业务场景]-[操作]-template-[版本].json
- 示例：medical-escort-booking-template-v1.json
- 存储在business_templates表中

### 7.2 技术栈

#### 7.2.1 核心技术选型

| 技术分类 | 技术选型 | 版本 | 说明 |
|---------|---------|------|------|
| **前端框架** | uni-app | 3.x | 多端统一开发框架 |
| **移动端UI** | uView UI | 3.0 | uni-app组件库 |
| **管理后台UI** | Element Plus | 2.x | Vue3组件库 |
| **后端框架** | Spring Boot | 3.2.x | 微服务框架 |
| **配置引擎** | 自研Template Engine | - | 业务流程引擎 |
| **AI框架** | Spring AI | 1.0.0-M1 | AI集成框架 |
| **数据库** | PostgreSQL | 15.x | 主数据库+pgvector |
| **缓存** | Redis | 7.0.x | 分布式缓存 |
| **消息队列** | RocketMQ | 5.x | 异步消息 |
| **API网关** | Spring Cloud Gateway | 4.x | 统一入口+MCP适配 |
| **监控** | Prometheus + Grafana | - | 指标监控 |
| **日志** | ELK Stack | 8.x | 日志系统 |
| **容器** | Docker + K8s | 24.x/1.28.x | 容器化部署 |
| **AI平台** | Dify | - | AI工作流编排 |
| **向量库** | Qdrant | 1.7.x | 知识库存储 |
| **对象存储** | MinIO | - | 文件存储 |



### 7.3 系统架构设计

#### 7.3.1 整体架构图

延康"云e养"平台采用模块化单体架构设计，通过配置驱动的方式实现业务的灵活性和可扩展性。

```mermaid
graph TB
    subgraph "展示层 (Presentation Layer)"
        UI1[微信小程序]
        UI2[H5 Web应用]
        UI3[管理后台]
        UI4[开放API]
    end
    
    subgraph "网关层 (Gateway Layer)"
        GW[Spring Cloud Gateway]
        subgraph "网关功能"
            GW1[认证授权]
            GW2[限流熔断]
            GW3[负载均衡]
            GW4[请求路由]
        end
    end
    
    subgraph "应用层 (Application Layer)"
        subgraph "Router层"
            R1[业务路由器]
            R2[Template选择器]
            R3[路由规则引擎]
        end
        
        subgraph "Engine层"
            E1[用户引擎]
            E2[订单引擎]
            E3[服务引擎]
            E4[支付引擎]
            E5[营销引擎]
        end
        
        subgraph "Component层"
            subgraph "业务组件"
                C1[用户组件]
                C2[订单组件]
                C3[支付组件]
                C4[通知组件]
                C5[健康档案组件]
                C6[服务调度组件]
            end
        end
    end
    
    subgraph "基础设施层 (Infrastructure Layer)"
        subgraph "Plugin层"
            P1[微信插件]
            P2[支付插件]
            P3[短信插件]
            P4[地图插件]
            P5[AI插件]
        end
        
        subgraph "Tool层"
            T1[数据库工具]
            T2[缓存工具]
            T3[消息队列工具]
            T4[日志工具]
            T5[加密工具]
            T6[文件工具]
        end
    end
    
    subgraph "配置层 (Configuration Layer)"
        TC[Template配置中心]
        subgraph "业务模板"
            TC1[用户注册模板]
            TC2[服务预约模板]
            TC3[订单处理模板]
            TC4[支付流程模板]
        end
    end
    
    subgraph "数据存储层 (Data Storage Layer)"
        DB1[(PostgreSQL<br/>主数据库)]
        DB2[(Redis<br/>缓存)]
        DB3[(MinIO<br/>对象存储)]
        DB4[(Elasticsearch<br/>搜索引擎)]
    end
    
    subgraph "基础服务 (Infrastructure Services)"
        INF1[Nacos配置中心]
        INF2[RocketMQ消息队列]
        INF3[ELK日志系统]
        INF4[Prometheus监控]
    end
    
    UI1 --> GW
    UI2 --> GW
    UI3 --> GW
    UI4 --> GW
    
    GW --> R1
    R1 --> TC
    R1 --> E1
    R1 --> E2
    R1 --> E3
    R1 --> E4
    R1 --> E5
    
    E1 --> C1
    E2 --> C2
    E3 --> C3
    E4 --> C4
    E5 --> C5
    E5 --> C6
    
    C1 --> P1
    C2 --> P2
    C3 --> P3
    C4 --> P4
    C5 --> P5
    
    C1 --> T1
    C2 --> T2
    C3 --> T3
    C4 --> T4
    C5 --> T5
    C6 --> T6
    
    T1 --> DB1
    T2 --> DB2
    T3 --> DB3
    T4 --> DB4
    
    TC --> DB1
```

#### 7.3.2 技术架构分层详解

**1. 展示层（Presentation Layer）**
- **职责**：用户界面展示、用户交互处理
- **技术**：微信小程序、Vue.js、React
- **特点**：多端适配、响应式设计、适老化界面

**2. 网关层（Gateway Layer）**
- **职责**：统一入口、请求路由、认证授权、限流熔断
- **技术**：Spring Cloud Gateway
- **核心功能**：
  - JWT认证
  - 基于令牌桶的限流
  - 动态路由配置
  - 请求日志记录

**3. 应用层（Application Layer）**
- **Router层**：
  - 解析请求，选择合适的Template
  - 根据业务规则进行路由决策
  - 调用对应的Engine执行业务
  
- **Engine层**：
  - 每个业务模块都有对应的Engine
  - 解析Template配置
  - 编排Component执行业务流程
  - 处理事务和异常
  
- **Component层**：
  - 原子化的业务功能实现
  - 可被多个Engine复用
  - 高内聚、低耦合

**4. 基础设施层（Infrastructure Layer）**
- **Plugin层**：第三方服务集成
- **Tool层**：基础技术工具
- **特点**：统一封装、易于扩展、错误处理

**5. 配置层（Configuration Layer）**
- **Template配置中心**：集中管理所有业务流程配置
- **配置热更新**：支持不停机更新业务流程
- **版本管理**：配置版本控制和回滚

**6. 数据存储层（Data Storage Layer）**
- **PostgreSQL**：核心业务数据和Template配置
- **Redis**：缓存和分布式锁
- **MinIO**：文件和图片存储
- **Elasticsearch**：全文搜索和日志分析



### 7.4 动态架构实现设计

#### 7.4.1 核心组件职责

延康"云e养"平台的动态架构通过以下核心组件协作实现：

**Gateway（API网关）**：
- 统一入口管理
- 认证与授权
- 限流与熔断
- 请求转发到Router

**Router（业务路由器）**：
- 解析请求特征
- 匹配路由规则
- 选择合适的Template
- 调用Engine执行

**Engine（执行引擎）**：
- 解析Template配置
- 协调Component执行
- 管理执行上下文
- 处理事务和异常

**Component（业务组件）**：
- 实现原子化业务功能
- 可被Template配置调用
- 不依赖具体流程

#### 7.4.2 Spring注解映射

**架构层次与实现映射**：

| 架构层次 | 实现方式 | Spring注解 | 示例 |
|---------|---------|-----------|------|
| **Gateway** | Spring Cloud Gateway | 配置类 | gateway-routes.yml |
| **Router** | 自研组件 | @Component | BusinessRouter.java |
| **Template** | 数据库存储 | - | business_templates表 |
| **Engine** | 通用引擎 | @Service | TemplateEngine.java |
| **Component** | 业务组件 | @Component | UserComponent.java |
| **Plugin** | 第三方集成 | @Component | WechatPlugin.java |
| **Tool** | 基础工具 | @Component | DatabaseTool.java |

#### 7.4.3 请求处理流程

**完整的请求处理链路**：

```mermaid
graph LR
    subgraph "请求流程"
        A[客户端请求] --> B[Gateway认证]
        B --> C[Router路由]
        C --> D[查询Template]
        D --> E[Engine执行]
        E --> F[Component处理]
        F --> G[返回结果]
    end
```

**关键点**：
1. 无需编写Controller，所有请求统一处理
2. 业务逻辑完全由Template配置定义
3. 新业务只需配置新的Template和路由规则
4. Component保持独立，可复用于不同流程

### 7.5 配置驱动设计

#### 7.5.1 配置驱动实现细节

**配置驱动架构图**：

```mermaid
graph TB
    subgraph "配置层"
        A1["业务流程配置<br/>Template JSON"]
        A2["规则配置<br/>Rule JSON"]
        A3["参数配置<br/>Parameter JSON"]
    end
    
    subgraph "存储层"
        B1["PostgreSQL<br/>business_templates表"]
        B2["版本管理<br/>template_versions表"]
        B3["配置历史<br/>template_history表"]
    end
    
    subgraph "管理层"
        C1["配置管理界面<br/>Web Console"]
        C2["配置API<br/>RESTful接口"]
        C3["配置验证<br/>Schema校验"]
    end
    
    subgraph "执行层"
        D1["配置加载器<br/>TemplateLoader"]
        D2["配置解析器<br/>TemplateParser"]
        D3["执行引擎<br/>Engine层"]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    
    B1 --> C1
    B1 --> C2
    
    C1 --> D1
    C2 --> D1
    D1 --> D2
    D2 --> D3
```

#### 7.5.2 Template配置设计

**用户注册流程配置（user-registration.json）**：
```json
{
  "templateId": "user-registration-v1.0",
  "templateName": "用户注册流程",
  "version": "1.0",
  "steps": [
    {
      "stepId": "validate-input",
      "stepName": "输入验证",
      "component": "ValidationComponent",
      "method": "validateRegistration",
      "parameters": {
        "rules": ["mobile", "password", "verificationCode"]
      }
    },
    {
      "stepId": "check-duplicate",
      "stepName": "重复检查",
      "component": "UserComponent",
      "method": "checkDuplicateUser",
      "parameters": {
        "checkFields": ["mobile", "email"]
      }
    },
    {
      "stepId": "create-user",
      "stepName": "创建用户",
      "component": "UserComponent",
      "method": "createUser",
      "parameters": {
        "autoActivate": true
      }
    },
    {
      "stepId": "send-welcome",
      "stepName": "发送欢迎消息",
      "component": "NotificationComponent",
      "method": "sendWelcomeMessage",
      "parameters": {
        "messageType": "sms",
        "template": "welcome-template"
      }
    }
  ],
  "errorHandling": {
    "retryPolicy": {
      "maxRetries": 3,
      "backoffInterval": 1000
    },
    "failureActions": [
      {
        "condition": "ValidationException",
        "action": "returnError"
      },
      {
        "condition": "DuplicateUserException",
        "action": "returnError"
      }
    ]
  }
}
```


#### 7.5.3 模板处理器设计

**模板处理器（TemplateProcessorTool）**：
该工具负责从PostgreSQL数据库的business_templates表中读取Template配置数据，并提供给Engine层使用。（实现代码见第10章）

**配置加载策略**：
- **预加载**：系统启动时加载常用配置到缓存
- **懒加载**：首次使用时从数据库加载
- **热更新**：配置变更时自动刷新缓存
- **失效机制**：定期清理未使用的配置缓存

#### 7.5.4 配置管理实现

**配置版本控制**：
- 每个Template配置保存历史版本
- 支持配置回滚和对比
- 使用business_templates和template_versions表存储

**配置验证机制**：

通过TemplateValidator组件实现JSON Schema验证、组件引用检查、循环依赖检测等功能。（实现代码见第10章）

**配置热更新**：
- 使用Redis发布订阅机制通知配置更新
- 各节点监听配置变更事件，自动刷新本地缓存
- 支持灰度发布，按用户比例逐步切换新配置

### 7.6 前端架构设计

#### 7.6.1 前端架构分层

```mermaid
graph TB
    subgraph "展示层 (Presentation Layer)"
        UI1[微信小程序]
        UI2[H5 Web应用]
        UI3[管理后台]
        UI4[语音助手界面]
    end
    
    subgraph "应用框架层 (Application Framework)"
        F1[uni-app框架]
        F2[Vue 3.0]
        F3[微信原生API]
        F4[Web API]
    end
    
    subgraph "动态渲染层 (Dynamic Rendering)"
        DR1[Template解析引擎]
        DR2[组件动态加载器]
        DR3[数据绑定引擎]
        DR4[样式动态处理]
        DR5[指令解析器<br/>v-for/v-if/v-show]
    end
    
    subgraph "组件系统层 (Component System)"
        subgraph "基础组件"
            BC1[uView UI组件]
            BC2[Element Plus组件]
            BC3[原生小程序组件]
        end
        
        subgraph "业务组件"
            BZ1[适老化增强组件]
            BZ2[健康服务组件]
            BZ3[订单流程组件]
            BZ4[语音交互组件]
        end
        
        subgraph "容器组件"
            CC1[StepLayout步骤布局]
            CC2[FormLayout表单布局]
            CC3[ListLayout列表布局]
            CC4[GridLayout网格布局]
        end
    end
    
    subgraph "状态管理层 (State Management)"
        SM1[Pinia Store]
        SM2[Local Storage]
        SM3[Session Storage]
        SM4[Template Context]
    end
    
    subgraph "服务交互层 (Service Layer)"
        subgraph "API管理"
            API1[Request拦截器]
            API2[Response处理器]
            API3[错误处理器]
            API4[Token管理]
        end
        
        subgraph "通信协议"
            CP1[RESTful API]
            CP2[WebSocket]
            CP3[SSE推送]
        end
    end
    
    subgraph "基础设施层 (Infrastructure)"
        INF1[路由系统]
        INF2[权限控制]
        INF3[缓存机制]
        INF4[日志系统]
        INF5[异常监控]
        INF6[性能监控]
    end
    
    subgraph "适老化支持层 (Elderly Support)"
        ES1[语音识别SDK]
        ES2[语音合成SDK]
        ES3[大字体系统]
        ES4[高对比度主题]
        ES5[手势简化]
        ES6[操作引导]
    end
    
    UI1 --> F1
    UI2 --> F1
    UI3 --> F2
    UI4 --> F3
    
    F1 --> DR1
    F2 --> DR1
    F3 --> DR2
    F4 --> DR3
    
    DR1 --> BC1
    DR2 --> BZ1
    DR3 --> CC1
    DR4 --> ES4
    DR5 --> SM4
    
    BC1 --> API1
    BZ1 --> SM1
    CC1 --> CP1
    
    API1 --> INF1
    SM1 --> INF3
    CP1 --> INF5
    
    ES1 --> BZ4
    ES2 --> BZ4
```

#### 7.6.2 组件体系设计

**基于uni-app的组件方案**：

| 组件类型 | 技术选择 | 说明 |
|---------|----------|------|
| **基础组件** | uView UI 3.0 | 完整的uni-app组件库 |
| **适老化适配** | CSS变量覆盖 | 全局样式定制 |
| **核心封装** | 自定义组件 | 仅封装必要的增强功能 |

**组件层次结构**：
```
uView UI基础组件
    ↓
适老化样式主题（CSS Variables）
    ↓  
业务增强组件（语音、震动等）
    ↓
页面级组件
```

**必要的封装组件**：
- ElderlyButton：增强按钮（震动+语音）
- VoiceInput：语音输入组件
- LargeText：大字体文本展示
- SimpleSteps：简化步骤条

#### 7.6.3 动态渲染机制

**支持的动态指令**：

前端支持v-for、v-if等动态指令，可以根据Template配置动态渲染页面元素。（示例代码见第10章）

**渲染引擎工作流程**：
1. 解析Template中的UI配置
2. 识别动态指令（v-for、v-if等）
3. 绑定数据源
4. 调用对应组件渲染
5. 应用样式和交互

#### 7.6.4 前后端协同机制

**Template驱动的前后端统一**：

前端通过解析Template中的uiConfig渲染页面，后端通过businessFlow处理业务逻辑，实现前后端的配置化协同。

#### 7.6.5 适老化设计实现

**全局适老化配置**：

通过CSS变量定义全局适老化样式，包括字体大小、按钮高度、颜色对比度等。（完整样式见第10章）

## 8. AI集成架构设计

### 8.1 AI技术架构总览

延康"云e养"平台通过Spring AI框架、Dify平台和MCP协议构建了完整的AI能力体系，实现AI与业务系统的深度融合。

```mermaid
graph TB
    subgraph "AI应用层"
        A1[AI健康管家]
        A2[智能客服]
        A3[健康分析]
        A4[服务推荐]
    end
    
    subgraph "AI框架层"
        B1[Spring AI<br/>AI功能集成]
        B2[Dify<br/>AI工作流编排]
        B3[MCP协议<br/>AI工具调用]
    end
    
    subgraph "模型服务层"
        C1[OpenAI GPT-4]
        C2[通义千问]
        C3[文心一言]
        C4[Ollama本地模型]
    end
    
    subgraph "数据层"
        D1[PostgreSQL pgvector<br/>向量存储]
        D2[Qdrant<br/>向量数据库]
        D3[知识库<br/>RAG数据]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B1
    A4 --> B3
    
    B1 --> C1
    B1 --> C2
    B2 --> C3
    B3 --> C4
    
    B1 --> D1
    B2 --> D2
    B3 --> D3
```

### 8.2 MCP协议集成

#### 8.2.1 集成架构设计

延康"云e养"平台通过Gateway层集成MCP协议，实现AI模型与业务系统的安全交互。

```mermaid
graph TB
    subgraph "AI应用层"
        A1["Dify平台<br/>AI工作流编排"]
        A2["AI Agent<br/>智能决策体"]
        A3["LLM模型<br/>语言理解"]
    end
    
    subgraph "Gateway集成层"
        B1["Spring Cloud Gateway"]
        B2["MCP协议适配器"]
        B3["工具路由映射"]
        B4["安全与鉴权"]
    end
    
    subgraph "内部服务层"
        C1["健康服务"]
        C2["订单服务"]
        C3["用户服务"]
        C4["数据服务"]
    end
    
    A1 -->|MCP协议| B1
    A2 -->|MCP协议| B1
    B1 --> B2
    B2 --> B3
    B3 --> C1
    B3 --> C2
    B3 --> C3
    B3 --> C4
```

#### 8.2.2 MCP工具定义

**核心工具集**：

```json
{
  "tools": [
    {
      "name": "analyze_health_report",
      "description": "分析用户健康报告，提供专业解读",
      "parameters": {
        "userId": {"type": "string", "description": "用户ID"},
        "reportData": {"type": "object", "description": "报告数据"}
      }
    },
    {
      "name": "create_service_order", 
      "description": "创建上门服务订单",
      "parameters": {
        "serviceType": {"type": "string", "description": "服务类型"},
        "appointmentTime": {"type": "datetime", "description": "预约时间"},
        "address": {"type": "object", "description": "服务地址"}
      }
    },
    {
      "name": "query_health_metrics",
      "description": "查询健康指标历史数据",
      "parameters": {
        "metricType": {"type": "string", "description": "指标类型"},
        "timeRange": {"type": "string", "description": "时间范围"}
      }
    }
  ]
}
```

#### 8.2.3 Gateway层实现

**MCP适配器实现**：

MCPGatewayAdapter组件负责处理MCP请求，包括验证请求合法性、获取工具映射、转换参数、调用内部API等功能。（实现代码见第10章）

### 8.3 AI应用场景与实现

#### 8.3.1 AI健康管家实现

**功能架构**：

```mermaid
graph LR
    A[用户输入] --> B[自然语言理解]
    B --> C[意图识别]
    C --> D[MCP工具调用]
    D --> E[业务服务]
    E --> F[结果整合]
    F --> G[自然语言生成]
    G --> H[用户反馈]
```

**核心实现**：

AIHealthAssistantComponent提供健康咨询、报告分析等AI能力，通过Spring AI进行RAG检索、生成响应、提取指标等。（实现代码见第10章）

#### 8.3.2 智能客服助手

**能力矩阵**：

| 场景类型 | AI能力 | MCP工具 | 自动化程度 |
|---------|--------|---------|------------|
| 服务咨询 | 理解需求、推荐服务 | query_services | 100% |
| 订单处理 | 创建、查询、修改订单 | order_operations | 90% |
| 健康建议 | 分析状况、给出建议 | health_analysis | 85% |
| 投诉处理 | 理解问题、协调解决 | complaint_handling | 70% |

### 8.4 数据安全与隐私

#### 8.4.1 AI数据安全控制

**安全策略**：
- **身份验证**：所有MCP请求必须包含有效的API密钥
- **权限控制**：基于工具的细粒度权限管理
- **数据脱敏**：敏感数据在返回AI前自动脱敏
- **审计日志**：记录所有MCP调用的详细日志

**实现示例**：

MCPSecurityFilter组件实现API密钥验证、工具权限检查、审计日志记录等安全控制功能。（完整代码见第10章）

#### 8.4.2 医疗数据隐私保护

**数据脱敏策略**：

HealthDataMaskingComponent组件对返回AI的数据进行脱敏处理，包括身份证号、手机号、病历敏感信息等。（实现代码见第10章）

## 9. 系统治理与运维

### 9.1 安全架构设计

#### 9.1.1 多层安全防护体系

**安全架构**：

```mermaid
graph TB
    subgraph "网络安全层"
        A1["WAF防火墙<br/>SQL注入/XSS防护"]
        A2["DDoS防护<br/>流量清洗"]
        A3["CDN加速<br/>隐藏真实IP"]
    end
    
    subgraph "应用安全层"
        B1["身份认证<br/>JWT+OAuth2"]
        B2["接口鉴权<br/>RBAC权限模型"]
        B3["数据加密<br/>AES-256+RSA"]
        B4["敏感数据脱敏<br/>动态脱敏"]
    end
    
    subgraph "数据安全层"
        C1["数据库加密<br/>透明数据加密"]
        C2["备份加密<br/>增量备份加密"]
        C3["传输加密<br/>TLS 1.3"]
        C4["存储加密<br/>文件系统加密"]
    end
    
    subgraph "基础设施安全"
        D1["容器安全<br/>镜像漏洞扫描"]
        D2["主机安全<br/>入侵检测"]
        D3["网络隔离<br/>VPC+安全组"]
        D4["密钥管理<br/>KMS密钥轮换"]
    end
    
    A1 --> B1
    A2 --> B2
    A3 --> B3
    
    B1 --> C1
    B2 --> C2
    B3 --> C3
    B4 --> C4
    
    C1 --> D1
    C2 --> D2
    C3 --> D3
    C4 --> D4
```

#### 9.1.2 数据隐私保护机制

**医疗数据隐私保护**：

根据《个人信息保护法》和《数据安全法》要求，建立医疗数据隐私保护体系。

**数据分级分类管理**：

| 数据级别 | 数据类型 | 安全措施 | 访问控制 |
|---------|---------|---------|---------|
| **高敏感** | 健康档案、病历、检查报告 | 端到端加密+数字签名 | 医生权限+用户授权 |
| **中敏感** | 个人身份信息、联系方式 | AES-256加密+访问日志 | 角色权限+操作审计 |
| **低敏感** | 服务偏好、使用习惯 | 数据脱敏+匿名化 | 基础权限+统计分析 |
| **公开数据** | 服务介绍、价格信息 | 标准传输加密 | 公开访问 |

### 9.2 性能架构设计

#### 9.2.1 性能目标与指标

**性能指标定义**：

| 性能指标 | 目标值 | 监控方式 | 优化策略 |
|---------|--------|----------|----------|
| **接口响应时间** | P95 < 500ms | APM监控 | 缓存优化、数据库优化 |
| **系统吞吐量** | 10000 QPS | 压力测试 | 水平扩展、负载均衡 |
| **数据库性能** | 查询 < 100ms | 慢查询日志 | 索引优化、查询优化 |
| **缓存命中率** | > 95% | Redis监控 | 缓存策略优化 |
| **系统可用性** | 99.9% | 健康检查 | 容错设计、故障转移 |

### 9.3 监控运维架构

#### 9.3.1 监控体系设计

**全链路监控架构**：

```mermaid
graph TB
    subgraph "应用监控"
        A1["应用性能监控<br/>Micrometer"]
        A2["业务指标监控<br/>自定义指标"]
        A3["接口监控<br/>响应时间/错误率"]
    end
    
    subgraph "基础设施监控"
        B1["服务器监控<br/>CPU/内存/磁盘"]
        B2["数据库监控<br/>连接数/慢查询"]
        B3["缓存监控<br/>命中率/内存使用"]
    end
    
    subgraph "日志监控"
        C1["应用日志<br/>ELK Stack"]
        C2["访问日志<br/>Nginx日志"]
        C3["错误日志<br/>异常跟踪"]
    end
    
    subgraph "告警通知"
        D1["Prometheus<br/>指标采集"]
        D2["Alertmanager<br/>告警管理"]
        D3["Grafana<br/>可视化展示"]
    end
    
    A1 --> D1
    A2 --> D1
    A3 --> D1
    
    B1 --> D1
    B2 --> D1
    B3 --> D1
    
    C1 --> D2
    C2 --> D2
    C3 --> D2
    
    D1 --> D3
    D2 --> D3
```

#### 9.3.2 运维自动化

**CI/CD流水线**：

通过GitLab CI/CD实现自动化构建、测试和部署流程。（配置示例见第10章）

**自动化运维脚本**：

包括健康检查、服务重启、日志收集等自动化运维功能。（脚本示例见第10章）




## 10. 技术实现示例

本章展示核心组件的简化实现示例，仅供参考。

### 10.1 后端实现示例

**Router路由组件示例**：
```java
@Component
public class BusinessRouter {
    public Template route(HttpServletRequest request) {
        String path = request.getRequestURI();
        // 根据路径和条件匹配Template
        return templateRepository.findByPath(path);
    }
}
```

**Engine执行引擎示例**：
```java
@Service
public class TemplateEngine {
    public Object execute(Template template, Map<String, Object> input) {
        // 解析Template步骤并执行
        for (Step step : template.getSteps()) {
            Object component = getComponent(step.getComponentName());
            Object result = invokeMethod(component, step.getMethod(), input);
            input.put(step.getOutput(), result);
        }
        return input.get(\"result\");
    }
}
```

**业务Component示例**：
```java
@Component
public class OrderComponent {
    public OrderResult createOrder(Map<String, Object> params) {
        // 创建订单的业务逻辑
        Order order = new Order();
        order.setServiceType(params.get(\"serviceType\"));
        order.setAppointmentTime(params.get(\"appointmentTime\"));
        return orderRepository.save(order);
    }
}
```

### 10.2 Template配置示例

**完整的业务流程配置**：
```json
{
  \"templateId\": \"medical-escort-v1\",
  \"templateName\": \"就医陪诊服务\",
  \"businessFlow\": {
    \"steps\": [
      {
        \"component\": \"ValidationComponent\",
        \"method\": \"validateInput\",
        \"input\": {\"hospital\": \"$.hospital\", \"time\": \"$.time\"}
      },
      {
        \"component\": \"OrderComponent\",
        \"method\": \"createOrder\",
        \"input\": {\"type\": \"medical-escort\", \"data\": \"$.formData\"},
        \"output\": \"order\"
      },
      {
        \"component\": \"NotificationComponent\",
        \"method\": \"sendSMS\",
        \"input\": {\"orderId\": \"$.order.id\", \"mobile\": \"$.user.mobile\"}
      }
    ]
  }
}
```

### 10.3 前端实现示例

**uni-app页面示例**：
```vue
<template>
  <view class=\"page\">
    <u-steps :list=\"steps\" :current=\"currentStep\"></u-steps>
    
    <swiper :current=\"currentStep\">
      <swiper-item>
        <view class=\"step-content\">
          <elderly-button text=\"选择医院\" @click=\"selectHospital\" />
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentStep: 0,
      steps: [
        { title: '选择医院' },
        { title: '选择时间' },
        { title: '确认预约' }
      ]
    }
  },
  methods: {
    selectHospital() {
      // 跳转到医院选择页面
      uni.navigateTo({ url: '/pages/hospital/list' })
    }
  }
}
</script>
```

**适老化组件示例**：
```vue
<\!-- components/elderly-button.vue -->
<template>
  <button class=\"elderly-btn\" @tap=\"handleTap\">
    <slot>{{ text }}</slot>
  </button>
</template>

<script>
export default {
  props: ['text'],
  methods: {
    handleTap() {
      uni.vibrateShort() // 震动反馈
      this.$emit('click')
    }
  }
}
</script>

<style>
.elderly-btn {
  min-height: 120rpx;
  font-size: 40rpx;
  font-weight: bold;
}
</style>
```

### 10.4 配置管理实现示例

**Template配置处理器**：
```java
@Component
public class TemplateProcessorTool {
    /**
     * 从数据库读取并执行模板流程
     */
    public <T> T executeTemplate(String templateId, Object input, Class<T> resultType);
    
    /**
     * 验证模板配置
     */
    public ValidationResult validateTemplate(String templateId);
    
    /**
     * 从数据库动态加载模板
     */
    public boolean loadTemplate(String templateId, String templateContent);
    
    /**
     * 模板执行监控
     */
    public ExecutionMetrics getExecutionMetrics(String templateId);
}
```

**配置验证器**：
```java
@Component
public class TemplateValidator {
    public ValidationResult validate(String templateJson) {
        // JSON Schema验证
        // 组件引用检查
        // 循环依赖检测
        return result;
    }
}
```

**用户注册流程配置**：
```json
{
  "templateId": "user-registration-v1.0",
  "templateName": "用户注册流程",
  "version": "1.0",
  "steps": [
    {
      "stepId": "validate-input",
      "stepName": "输入验证",
      "component": "ValidationComponent",
      "method": "validateRegistration",
      "parameters": {
        "rules": ["mobile", "password", "verificationCode"]
      }
    },
    {
      "stepId": "check-duplicate",
      "stepName": "重复检查",
      "component": "UserComponent",
      "method": "checkDuplicateUser",
      "parameters": {
        "checkFields": ["mobile", "email"]
      }
    },
    {
      "stepId": "create-user",
      "stepName": "创建用户",
      "component": "UserComponent",
      "method": "createUser",
      "parameters": {
        "autoActivate": true
      }
    }
  ]
}
```

### 10.5 完整的Template配置示例

**就医陪诊服务配置（escort-service-template-v3.json）**：
```json
{
  "templateId": "medical-escort-v3",
  "templateName": "就医陪诊服务",
  "version": "3.0.0",
  
  // 路由配置
  "routing": {
    "pathPattern": "/api/v1/medical/escort/*",
    "requiredAuth": true,
    "userConditions": {
      "elderlyMode": true,
      "minAge": 60
    }
  },
  
  // 前端UI配置
  "uiConfig": {
    "layout": "step-layout",
    "theme": "elderly-friendly",
    "steps": [
      {
        "stepId": "select-hospital",
        "title": "选择就诊医院",
        "components": [
          {
            "type": "SearchableSelect",
            "key": "hospital",
            "props": {
              "placeholder": "请输入或选择医院",
              "dataSource": "$.nearbyHospitals",
              "searchable": true,
              "voiceInput": true
            },
            "style": {
              "fontSize": "20px",
              "height": "56px"
            },
            "validation": {
              "required": true,
              "message": "请选择医院"
            }
          }
        ]
      }
    ]
  },
  
  // 后端业务流程
  "businessFlow": {
    "steps": [
      {
        "step": "验证输入",
        "component": "ValidationComponent",
        "method": "validateEscortBooking",
        "input": {
          "hospital": "$.hospital",
          "time": "$.appointmentTime",
          "staffId": "$.selectedStaffId"
        }
      },
      {
        "step": "创建订单",
        "component": "OrderComponent",
        "method": "createEscortOrder",
        "transaction": true,
        "input": {
          "userId": "$.currentUser.id",
          "serviceDetails": {
            "type": "medical-escort",
            "hospital": "$.hospital",
            "appointmentTime": "$.appointmentTime"
          }
        },
        "output": "order"
      }
    ]
  }
}
```

### 10.6 前端动态渲染示例

**动态指令配置**：
```json
{
  "components": [
    {
      "type": "List",
      "props": {
        "dataSource": "$.staffList",
        "renderItem": {
          "v-for": "staff in staffList",
          "v-if": "staff.available",
          "component": "StaffCard",
          "props": {
            "name": "{{ staff.name }}",
            "rating": "{{ staff.rating }}",
            "photo": "{{ staff.photo }}"
          },
          "style": {
            "marginBottom": "{{ $index < staffList.length - 1 ? '16px' : '0' }}"
          }
        }
      }
    }
  ]
}
```

**适老化CSS变量**：
```scss
// 适老化CSS变量
:root {
  --elderly-font-base: 36rpx;
  --elderly-button-height: 120rpx;
  --elderly-color-primary: #0066CC;
  --elderly-spacing: 40rpx;
}
```

### 10.7 AI集成实现示例

**MCP适配器**：
```java
@Component
public class MCPGatewayAdapter {
    
    @Autowired
    private WebClient.Builder webClientBuilder;
    
    /**
     * 处理MCP请求
     */
    public Mono<MCPResponse> handleMCPRequest(ServerHttpRequest request, MCPRequest mcpRequest) {
        // 1. 验证请求合法性
        if (\!validateMCPRequest(mcpRequest)) {
            return Mono.error(new MCPValidationException("Invalid MCP request"));
        }
        
        // 2. 获取工具映射
        ToolMapping mapping = getToolMapping(mcpRequest.getTool());
        
        // 3. 转换参数
        Map<String, Object> apiParams = transformParameters(mcpRequest.getParameters(), mapping);
        
        // 4. 调用内部API
        return callInternalAPI(mapping.getApiPath(), apiParams)
            .map(response -> buildMCPResponse(response, mapping));
    }
    
    /**
     * 工具映射配置
     */
    private static final Map<String, ToolMapping> TOOL_MAPPINGS = Map.of(
        "analyze_health_report", new ToolMapping("/api/health/analyze", "POST"),
        "create_service_order", new ToolMapping("/api/order/create", "POST"),
        "query_health_metrics", new ToolMapping("/api/health/metrics", "GET")
    );
}
```

**AI健康管家组件**：
```java
@Component
public class AIHealthAssistantComponent {
    
    @Autowired
    private AIService aiService; // 使用Spring AI提供的能力
    
    /**
     * 健康咨询处理
     */
    public HealthConsultResult consult(String userId, String question) {
        // 1. 获取用户健康档案
        HealthProfile profile = getHealthProfile(userId);
        
        // 2. 使用Spring AI进行RAG检索
        List<Document> relevantDocs = aiService.searchKnowledge(question);
        
        // 3. 构建上下文并调用AI模型
        String response = aiService.generateResponse(
            question, profile, relevantDocs
        );
        
        // 4. 解析结果并生成建议
        return parseConsultResult(response);
    }
}
```

**数据脱敏组件**：
```java
@Component
public class HealthDataMaskingComponent {
    
    /**
     * 对返回AI的数据进行脱敏
     */
    public Map<String, Object> maskSensitiveData(Map<String, Object> data) {
        Map<String, Object> masked = new HashMap<>(data);
        
        // 身份证号脱敏
        if (masked.containsKey("idCard")) {
            masked.put("idCard", maskIdCard(data.get("idCard").toString()));
        }
        
        // 手机号脱敏
        if (masked.containsKey("mobile")) {
            masked.put("mobile", maskMobile(data.get("mobile").toString()));
        }
        
        return masked;
    }
    
    private String maskIdCard(String idCard) {
        return idCard.substring(0, 6) + "********" + idCard.substring(14);
    }
    
    private String maskMobile(String mobile) {
        return mobile.substring(0, 3) + "****" + mobile.substring(7);
    }
}
```

### 10.8 运维自动化示例

**CI/CD流水线配置**：
```yaml
# .gitlab-ci.yml
stages:
  - build
  - test
  - deploy

build:
  stage: build
  script:
    - ./mvnw clean package -DskipTests
    - docker build -t aie/identity-service:$CI_COMMIT_SHA .
    - docker push aie/identity-service:$CI_COMMIT_SHA

test:
  stage: test
  script:
    - ./mvnw test
    - ./mvnw sonar:sonar

deploy:
  stage: deploy
  script:
    - kubectl set image deployment/identity-service identity-service=aie/identity-service:$CI_COMMIT_SHA
    - kubectl rollout status deployment/identity-service
  only:
    - main
```

**健康检查脚本**：
```bash
#\!/bin/bash
# 健康检查脚本
SERVICE_NAME="identity-service"
HEALTH_URL="http://localhost:8080/actuator/health"

check_service_health() {
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" $HEALTH_URL)
    if [ $HTTP_CODE -eq 200 ]; then
        echo "$(date): $SERVICE_NAME is healthy"
        return 0
    else
        echo "$(date): $SERVICE_NAME is unhealthy (HTTP $HTTP_CODE)"
        return 1
    fi
}

# 执行健康检查
if check_service_health; then
    exit 0
else
    # 服务不健康，尝试重启
    echo "$(date): Restarting $SERVICE_NAME..."
    kubectl rollout restart deployment/$SERVICE_NAME
    exit 1
fi
```
